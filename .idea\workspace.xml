<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="72491f2c-9760-43c6-b489-ee98e32fa288" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$" beforeDir="true" afterPath="$PROJECT_DIR$" afterDir="true" />
      <change beforePath="$PROJECT_DIR$/sms-ws-controller/src/main/resources/application.properties" beforeDir="false" afterPath="$PROJECT_DIR$/sms-ws-controller/src/main/resources/application.properties" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ChangesViewManager">
    <option name="groupingKeys">
      <option value="module" />
    </option>
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHomeTypeForPersistence" value="WRAPPER" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 8
}</component>
  <component name="ProjectId" id="2x4l5nOfvWWqE8HnpCx2in76r51" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/dev/ui-pmi&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager">
    <configuration name="Application" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="sms-ws-controller" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.tango.sms.ws.controller.Application" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="SvnConfiguration" cleanupOnStartRun="true">
    <configuration>C:\Users\<USER>\AppData\Roaming\Subversion</configuration>
    <supportedVersion>125</supportedVersion>
  </component>
  <component name="SvnFileUrlMappingImpl">
    <option name="myMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Users\<USER>\dev\sms-ws" />
          <option name="myCopyRoot" value="C:\Users\<USER>\dev\sms-ws" />
        </SvnCopyRootSimple>
      </list>
    </option>
    <option name="myMoreRealMappingRoots">
      <list>
        <SvnCopyRootSimple>
          <option name="myVcsRoot" value="C:\Users\<USER>\dev\sms-ws" />
          <option name="myCopyRoot" value="C:\Users\<USER>\dev\sms-ws" />
        </SvnCopyRootSimple>
      </list>
    </option>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="72491f2c-9760-43c6-b489-ee98e32fa288" name="Changes" comment="" />
      <created>1747205045757</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747205045757</updated>
      <workItem from="1747205046811" duration="6609000" />
      <workItem from="1749450283602" duration="57000" />
    </task>
    <task id="LOCAL-00001" summary="CORS Support">
      <option name="closed" value="true" />
      <created>1747215769040</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1747215769040</updated>
    </task>
    <task id="LOCAL-00002" summary="CORS Support">
      <option name="closed" value="true" />
      <created>1747216379938</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1747216379938</updated>
    </task>
    <task id="LOCAL-00003" summary="CORS Support">
      <option name="closed" value="true" />
      <created>1747218443136</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1747218443136</updated>
    </task>
    <option name="localTasksCounter" value="4" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="CORS Support" />
    <option name="LAST_COMMIT_MESSAGE" value="CORS Support" />
  </component>
</project>