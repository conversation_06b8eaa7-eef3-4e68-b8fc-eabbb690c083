package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QDeliverEncodingProfile is a Querydsl query type for DeliverEncodingProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QDeliverEncodingProfile extends BeanPath<DeliverEncodingProfile> {

    private static final long serialVersionUID = -757294201L;

    public static final QDeliverEncodingProfile deliverEncodingProfile = new QDeliverEncodingProfile("deliverEncodingProfile");

    public final EnumPath<com.tango.sms.ws.model.enumeration.DataCodingStandard> defaultStandardForDeliveryOfSms = createEnum("defaultStandardForDeliveryOfSms", com.tango.sms.ws.model.enumeration.DataCodingStandard.class);

    public QDeliverEncodingProfile(String variable) {
        super(DeliverEncodingProfile.class, forVariable(variable));
    }

    public QDeliverEncodingProfile(Path<? extends DeliverEncodingProfile> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDeliverEncodingProfile(PathMetadata metadata) {
        super(DeliverEncodingProfile.class, metadata);
    }

}

