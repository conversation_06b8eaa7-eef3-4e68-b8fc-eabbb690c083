--
-- Table structure for table `charging_method`
--
CREATE TABLE `charging_method` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `config_file_index` int NOT NULL,
  `a_number_prepaid_list_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKt3sll7p7mmnhadb0njie6vhgb` (`a_number_prepaid_list_id`),
  CONSTRAINT `FKt3sll7p7mmnhadb0njie6vhgb` FOREIGN KEY (`a_number_prepaid_list_id`) REFERENCES `number_list` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

--
-- Table structure for table `number_analysis`
--
CREATE TABLE `number_analysis` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `charging_method_reload_host` varchar(255) DEFAULT NULL,
  `a_number_black_list_id` bigint DEFAULT NULL,
  `b_number_black_list_id` bigint DEFAULT NULL,
  `b_number_white_list_id` bigint DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK3odtwo2qxbq4dwlxl60jv1pgr` (`a_number_black_list_id`),
  KEY `FKg57cvwac700rar2ud64a368us` (`b_number_black_list_id`),
  KEY `FK61po9t3m1mlcw45djnww8kmy2` (`b_number_white_list_id`),
  CONSTRAINT `FK3odtwo2qxbq4dwlxl60jv1pgr` FOREIGN KEY (`a_number_black_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FK61po9t3m1mlcw45djnww8kmy2` FOREIGN KEY (`b_number_white_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FKg57cvwac700rar2ud64a368us` FOREIGN KEY (`b_number_black_list_id`) REFERENCES `number_list` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

--
-- Table structure for table `number_analysis_a_number_white_list`
--
CREATE TABLE `number_analysis_a_number_white_list` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `charging_override_enabled` bit(1) NOT NULL,
  `a_number_white_list_id` bigint NOT NULL,
  `charging_method_id` bigint DEFAULT NULL,
  `number_analysis_id` bigint DEFAULT NULL,
  `a_number_white_list_entry_order` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKrdycesu1ohfr7dyd5apkpw993` (`a_number_white_list_id`),
  KEY `FKac8v76yctypxgi08ajk17b5sb` (`charging_method_id`),
  KEY `FKhyybq95xo1375acwv2cv1xtj8` (`number_analysis_id`),
  CONSTRAINT `FKac8v76yctypxgi08ajk17b5sb` FOREIGN KEY (`charging_method_id`) REFERENCES `charging_method` (`id`),
  CONSTRAINT `FKhyybq95xo1375acwv2cv1xtj8` FOREIGN KEY (`number_analysis_id`) REFERENCES `number_analysis` (`id`),
  CONSTRAINT `FKrdycesu1ohfr7dyd5apkpw993` FOREIGN KEY (`a_number_white_list_id`) REFERENCES `number_list` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

--
-- Table structure for table `number_analysis_charging_override`
--
CREATE TABLE `number_analysis_charging_override` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `b_number_prefix` varchar(255) NOT NULL,
  `charging_method_id` bigint DEFAULT NULL,
  `number_analysis_id` bigint DEFAULT NULL,
  `charging_override_entry_order` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKsus942upvt10tq3lgwvoe2x0a` (`charging_method_id`),
  KEY `FKn6wtyxoh83lagdx4bcwotert8` (`number_analysis_id`),
  CONSTRAINT `FKn6wtyxoh83lagdx4bcwotert8` FOREIGN KEY (`number_analysis_id`) REFERENCES `number_analysis` (`id`),
  CONSTRAINT `FKsus942upvt10tq3lgwvoe2x0a` FOREIGN KEY (`charging_method_id`) REFERENCES `charging_method` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

--
-- Table structure for table `strip_and_replace`
--
CREATE TABLE `strip_and_replace` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `ao_strip_and_replace_enabled` bit(1) NOT NULL,
  `mo_strip_and_replace_enabled` bit(1) NOT NULL,
  `type` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

--
-- Table structure for table `strip_and_replace_entry`
--
CREATE TABLE `strip_and_replace_entry` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `max_digits` int DEFAULT NULL,
  `min_digits` int DEFAULT NULL,
  `replace_number` varchar(255) DEFAULT NULL,
  `replace_number_plan_indicator` varchar(255) DEFAULT NULL,
  `replace_type_of_number` varchar(255) DEFAULT NULL,
  `strip_number` varchar(255) NOT NULL,
  `strip_number_plan_indicator` varchar(255) DEFAULT NULL,
  `strip_type_of_number` varchar(255) DEFAULT NULL,
  `strip_and_replace_id` bigint DEFAULT NULL,
  `strip_and_replace_entry_order` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK8pynmwewwwgmnwaw1cx420ifp` (`strip_and_replace_id`),
  CONSTRAINT `FK8pynmwewwwgmnwaw1cx420ifp` FOREIGN KEY (`strip_and_replace_id`) REFERENCES `strip_and_replace` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;