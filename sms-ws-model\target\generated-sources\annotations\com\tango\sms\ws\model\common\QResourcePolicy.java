package com.tango.sms.ws.model.common;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QResourcePolicy is a Querydsl query type for ResourcePolicy
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QResourcePolicy extends EntityPathBase<ResourcePolicy> {

    private static final long serialVersionUID = -1464305389L;

    public static final QResourcePolicy resourcePolicy = new QResourcePolicy("resourcePolicy");

    public final QBaseEntity _super = new QBaseEntity(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final StringPath name = createString("name");

    public QResourcePolicy(String variable) {
        super(ResourcePolicy.class, forVariable(variable));
    }

    public QResourcePolicy(Path<? extends ResourcePolicy> path) {
        super(path.getType(), path.getMetadata());
    }

    public QResourcePolicy(PathMetadata metadata) {
        super(ResourcePolicy.class, metadata);
    }

}

