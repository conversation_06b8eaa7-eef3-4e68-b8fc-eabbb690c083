package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QApplicationProfile is a Querydsl query type for ApplicationProfile
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QApplicationProfile extends EntityPathBase<ApplicationProfile> {

    private static final long serialVersionUID = -1776724017L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QApplicationProfile applicationProfile = new QApplicationProfile("applicationProfile");

    public final com.tango.sms.ws.model.common.QBaseEntity _super = new com.tango.sms.ws.model.common.QBaseEntity(this);

    public final SetPath<com.tango.sms.ws.model.common.AdditionalProperty, com.tango.sms.ws.model.common.QAdditionalProperty> additionalProperties = this.<com.tango.sms.ws.model.common.AdditionalProperty, com.tango.sms.ws.model.common.QAdditionalProperty>createSet("additionalProperties", com.tango.sms.ws.model.common.AdditionalProperty.class, com.tango.sms.ws.model.common.QAdditionalProperty.class, PathInits.DIRECT2);

    public final QBlackoutProfile blackoutProfile;

    public final QCdrProfile cdrProfile;

    public final QChargingProfile chargingProfile;

    public final QContentScreeningProfile contentScreeningProfile;

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    public final QDataCodingProfile dataCodingProfile;

    public final QDeliverEncodingProfile deliverEncodingProfile;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final StringPath name = createString("name");

    public final QNumberScreeningProfile numberScreeningProfile;

    public final QPriorityRetryProfile priorityRetryProfile;

    public final QReceiptProfile receiptProfile;

    public final QStorageProfile receiptStorageProfile;

    public final QStorageProfile storageProfile;

    public final QThrottlingProfile throttlingProfile;

    public QApplicationProfile(String variable) {
        this(ApplicationProfile.class, forVariable(variable), INITS);
    }

    public QApplicationProfile(Path<? extends ApplicationProfile> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QApplicationProfile(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QApplicationProfile(PathMetadata metadata, PathInits inits) {
        this(ApplicationProfile.class, metadata, inits);
    }

    public QApplicationProfile(Class<? extends ApplicationProfile> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.blackoutProfile = inits.isInitialized("blackoutProfile") ? new QBlackoutProfile(forProperty("blackoutProfile")) : null;
        this.cdrProfile = inits.isInitialized("cdrProfile") ? new QCdrProfile(forProperty("cdrProfile")) : null;
        this.chargingProfile = inits.isInitialized("chargingProfile") ? new QChargingProfile(forProperty("chargingProfile")) : null;
        this.contentScreeningProfile = inits.isInitialized("contentScreeningProfile") ? new QContentScreeningProfile(forProperty("contentScreeningProfile"), inits.get("contentScreeningProfile")) : null;
        this.dataCodingProfile = inits.isInitialized("dataCodingProfile") ? new QDataCodingProfile(forProperty("dataCodingProfile")) : null;
        this.deliverEncodingProfile = inits.isInitialized("deliverEncodingProfile") ? new QDeliverEncodingProfile(forProperty("deliverEncodingProfile")) : null;
        this.numberScreeningProfile = inits.isInitialized("numberScreeningProfile") ? new QNumberScreeningProfile(forProperty("numberScreeningProfile"), inits.get("numberScreeningProfile")) : null;
        this.priorityRetryProfile = inits.isInitialized("priorityRetryProfile") ? new QPriorityRetryProfile(forProperty("priorityRetryProfile"), inits.get("priorityRetryProfile")) : null;
        this.receiptProfile = inits.isInitialized("receiptProfile") ? new QReceiptProfile(forProperty("receiptProfile"), inits.get("receiptProfile")) : null;
        this.receiptStorageProfile = inits.isInitialized("receiptStorageProfile") ? new QStorageProfile(forProperty("receiptStorageProfile"), inits.get("receiptStorageProfile")) : null;
        this.storageProfile = inits.isInitialized("storageProfile") ? new QStorageProfile(forProperty("storageProfile"), inits.get("storageProfile")) : null;
        this.throttlingProfile = inits.isInitialized("throttlingProfile") ? new QThrottlingProfile(forProperty("throttlingProfile")) : null;
    }

}

