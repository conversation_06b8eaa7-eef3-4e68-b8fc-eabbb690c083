package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QFixedBlackoutDuration is a Querydsl query type for FixedBlackoutDuration
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QFixedBlackoutDuration extends EntityPathBase<FixedBlackoutDuration> {

    private static final long serialVersionUID = -1550451743L;

    public static final QFixedBlackoutDuration fixedBlackoutDuration = new QFixedBlackoutDuration("fixedBlackoutDuration");

    public final QBlackoutDuration _super = new QBlackoutDuration(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    public final DatePath<java.sql.Date> endDate = createDate("endDate", java.sql.Date.class);

    //inherited
    public final TimePath<java.sql.Time> endTime = _super.endTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final DatePath<java.sql.Date> startDate = createDate("startDate", java.sql.Date.class);

    //inherited
    public final TimePath<java.sql.Time> startTime = _super.startTime;

    public QFixedBlackoutDuration(String variable) {
        super(FixedBlackoutDuration.class, forVariable(variable));
    }

    public QFixedBlackoutDuration(Path<? extends FixedBlackoutDuration> path) {
        super(path.getType(), path.getMetadata());
    }

    public QFixedBlackoutDuration(PathMetadata metadata) {
        super(FixedBlackoutDuration.class, metadata);
    }

}

