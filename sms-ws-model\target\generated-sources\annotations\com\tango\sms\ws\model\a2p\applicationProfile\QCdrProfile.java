package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QCdrProfile is a Querydsl query type for CdrProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QCdrProfile extends BeanPath<CdrProfile> {

    private static final long serialVersionUID = -2125921330L;

    public static final QCdrProfile cdrProfile = new QCdrProfile("cdrProfile");

    public final BooleanPath auxServicesCdr = createBoolean("auxServicesCdr");

    public final BooleanPath chargingCdr = createBoolean("chargingCdr");

    public final BooleanPath enabled = createBoolean("enabled");

    public final BooleanPath finalCdr = createBoolean("finalCdr");

    public final BooleanPath receiptCdr = createBoolean("receiptCdr");

    public final BooleanPath retryCdr = createBoolean("retryCdr");

    public final BooleanPath submitCdr = createBoolean("submitCdr");

    public QCdrProfile(String variable) {
        super(CdrProfile.class, forVariable(variable));
    }

    public QCdrProfile(Path<? extends CdrProfile> path) {
        super(path.getType(), path.getMetadata());
    }

    public QCdrProfile(PathMetadata metadata) {
        super(CdrProfile.class, metadata);
    }

}

