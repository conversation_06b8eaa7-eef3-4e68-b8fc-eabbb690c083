package com.tango.sms.ws.model.common;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QStripAndReplace is a Querydsl query type for StripAndReplace
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QStripAndReplace extends EntityPathBase<StripAndReplace> {

    private static final long serialVersionUID = -1929174462L;

    public static final QStripAndReplace stripAndReplace = new QStripAndReplace("stripAndReplace");

    public final QBaseEntity _super = new QBaseEntity(this);

    public final BooleanPath aoStripAndReplaceEnabled = createBoolean("aoStripAndReplaceEnabled");

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final BooleanPath moStripAndReplaceEnabled = createBoolean("moStripAndReplaceEnabled");

    public final ListPath<StripAndReplaceEntry, QStripAndReplaceEntry> stripAndReplaceEntries = this.<StripAndReplaceEntry, QStripAndReplaceEntry>createList("stripAndReplaceEntries", StripAndReplaceEntry.class, QStripAndReplaceEntry.class, PathInits.DIRECT2);

    public final EnumPath<com.tango.sms.ws.model.enumeration.StripAndReplaceType> type = createEnum("type", com.tango.sms.ws.model.enumeration.StripAndReplaceType.class);

    public QStripAndReplace(String variable) {
        super(StripAndReplace.class, forVariable(variable));
    }

    public QStripAndReplace(Path<? extends StripAndReplace> path) {
        super(path.getType(), path.getMetadata());
    }

    public QStripAndReplace(PathMetadata metadata) {
        super(StripAndReplace.class, metadata);
    }

}

