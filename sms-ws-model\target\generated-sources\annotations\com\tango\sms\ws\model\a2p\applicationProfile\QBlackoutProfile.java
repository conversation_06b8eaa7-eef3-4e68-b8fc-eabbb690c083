package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QBlackoutProfile is a Querydsl query type for BlackoutProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QBlackoutProfile extends BeanPath<BlackoutProfile> {

    private static final long serialVersionUID = 984686052L;

    public static final QBlackoutProfile blackoutProfile = new QBlackoutProfile("blackoutProfile");

    public final SetPath<BlackoutPeriod, QBlackoutPeriod> blackoutPeriods = this.<BlackoutPeriod, QBlackoutPeriod>createSet("blackoutPeriods", BlackoutPeriod.class, QBlackoutPeriod.class, PathInits.DIRECT2);

    public final BooleanPath enabled = createBoolean("enabled");

    public QBlackoutProfile(String variable) {
        super(BlackoutProfile.class, forVariable(variable));
    }

    public QBlackoutProfile(Path<? extends BlackoutProfile> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBlackoutProfile(PathMetadata metadata) {
        super(BlackoutProfile.class, metadata);
    }

}

