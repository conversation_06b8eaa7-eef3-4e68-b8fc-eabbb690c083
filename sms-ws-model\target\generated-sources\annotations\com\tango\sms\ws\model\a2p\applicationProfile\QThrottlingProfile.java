package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QThrottlingProfile is a Querydsl query type for ThrottlingProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QThrottlingProfile extends BeanPath<ThrottlingProfile> {

    private static final long serialVersionUID = 1789340140L;

    public static final QThrottlingProfile throttlingProfile = new QThrottlingProfile("throttlingProfile");

    public final NumberPath<Long> analysisWindowInSeconds = createNumber("analysisWindowInSeconds", Long.class);

    public final NumberPath<Long> analysisWindowSMSThreshold = createNumber("analysisWindowSMSThreshold", Long.class);

    public final NumberPath<Long> analysisWindowThreshold = createNumber("analysisWindowThreshold", Long.class);

    public final BooleanPath enabled = createBoolean("enabled");

    public final NumberPath<Long> numberOfWindowsToAnalyse = createNumber("numberOfWindowsToAnalyse", Long.class);

    public QThrottlingProfile(String variable) {
        super(ThrottlingProfile.class, forVariable(variable));
    }

    public QThrottlingProfile(Path<? extends ThrottlingProfile> path) {
        super(path.getType(), path.getMetadata());
    }

    public QThrottlingProfile(PathMetadata metadata) {
        super(ThrottlingProfile.class, metadata);
    }

}

