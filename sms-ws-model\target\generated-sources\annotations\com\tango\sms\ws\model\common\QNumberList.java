package com.tango.sms.ws.model.common;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QNumberList is a Querydsl query type for NumberList
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QNumberList extends EntityPathBase<NumberList> {

    private static final long serialVersionUID = -1516557510L;

    public static final QNumberList numberList = new QNumberList("numberList");

    public final QBaseEntity _super = new QBaseEntity(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final StringPath name = createString("name");

    public final ListPath<NumberEntry, QNumberEntry> numberEntries = this.<NumberEntry, QNumberEntry>createList("numberEntries", NumberEntry.class, QNumberEntry.class, PathInits.DIRECT2);

    public final EnumPath<com.tango.sms.ws.model.enumeration.NumberListType> numberListType = createEnum("numberListType", com.tango.sms.ws.model.enumeration.NumberListType.class);

    public QNumberList(String variable) {
        super(NumberList.class, forVariable(variable));
    }

    public QNumberList(Path<? extends NumberList> path) {
        super(path.getType(), path.getMetadata());
    }

    public QNumberList(PathMetadata metadata) {
        super(NumberList.class, metadata);
    }

}

