Add scripts here for com.tango.sms.ws.model db migration

mysql> SHOW DATABASES;
mysql> CREATE DATABASE IF NOT EXISTS smswsdb DEFAULT CHARACTER SET utf8;
mysql> GRANT ALL PRIVILEGES ON smswsdb.* TO 'smswsjdbc'@'localhost' IDENTIFIED BY 'Smswss3cret!';
mysql> USE smswsdb;
mysql> SHOW TABLES;

To generate baseline:-
======================

[brian@brian1 trunk]$ mysqldump -uroot -p  --databases smswsdb > sms-ws-model/src/main/resources/db/migration/V1__Baseline.sql
Enter password:
[brian@brian1 trunk]$

NOTE:- remove table `schema_version` generated by mysqldump, and also any sample test data inserted.

