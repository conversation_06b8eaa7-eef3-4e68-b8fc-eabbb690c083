DELETE t1 FROM throttling_profile t1, throttling_profile t2 WHERE t1.id > t2.id AND t1.entity_name = t2.entity_name;
ALTER TABLE throttling_profile ADD UNIQUE (entity_name);

DELETE t1 FROM blackout_profile t1, blackout_profile t2 WHERE t1.id > t2.id AND t1.entity_name = t2.entity_name;
ALTER TABLE blackout_profile ADD UNIQUE (entity_name);

DELETE t1 FROM content_screening_profile t1, content_screening_profile t2 WHERE t1.id > t2.id AND t1.entity_name = t2.entity_name;
ALTER TABLE content_screening_profile ADD UNIQUE (entity_name);

DELETE t1 FROM number_screening_profile t1, number_screening_profile t2 WHERE t1.id > t2.id AND t1.entity_name = t2.entity_name;
ALTER TABLE number_screening_profile ADD UNIQUE (entity_name);