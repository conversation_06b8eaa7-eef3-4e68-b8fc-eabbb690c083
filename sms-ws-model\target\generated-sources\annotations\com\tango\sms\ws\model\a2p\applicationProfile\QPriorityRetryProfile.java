package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QPriorityRetryProfile is a Querydsl query type for PriorityRetryProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QPriorityRetryProfile extends BeanPath<PriorityRetryProfile> {

    private static final long serialVersionUID = 389771707L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QPriorityRetryProfile priorityRetryProfile = new QPriorityRetryProfile("priorityRetryProfile");

    public final EnumPath<com.tango.sms.ws.model.enumeration.PriorityLevel> defaultPriority = createEnum("defaultPriority", com.tango.sms.ws.model.enumeration.PriorityLevel.class);

    public final com.tango.sms.ws.model.common.QDeliveryAndRetryProfile emergencyPriorityDeliveryAndRetryProfile;

    public final com.tango.sms.ws.model.common.QDeliveryAndRetryProfile lowPriorityDeliveryAndRetryProfile;

    public final BooleanPath overrideMessagePriority = createBoolean("overrideMessagePriority");

    public final EnumPath<com.tango.sms.ws.model.enumeration.PriorityLevel> overridePriority = createEnum("overridePriority", com.tango.sms.ws.model.enumeration.PriorityLevel.class);

    public final com.tango.sms.ws.model.common.QDeliveryAndRetryProfile regularPriorityDeliveryAndRetryProfile;

    public final com.tango.sms.ws.model.common.QDeliveryAndRetryProfile urgentPriorityDeliveryAndRetryProfile;

    public QPriorityRetryProfile(String variable) {
        this(PriorityRetryProfile.class, forVariable(variable), INITS);
    }

    public QPriorityRetryProfile(Path<? extends PriorityRetryProfile> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QPriorityRetryProfile(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QPriorityRetryProfile(PathMetadata metadata, PathInits inits) {
        this(PriorityRetryProfile.class, metadata, inits);
    }

    public QPriorityRetryProfile(Class<? extends PriorityRetryProfile> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.emergencyPriorityDeliveryAndRetryProfile = inits.isInitialized("emergencyPriorityDeliveryAndRetryProfile") ? new com.tango.sms.ws.model.common.QDeliveryAndRetryProfile(forProperty("emergencyPriorityDeliveryAndRetryProfile"), inits.get("emergencyPriorityDeliveryAndRetryProfile")) : null;
        this.lowPriorityDeliveryAndRetryProfile = inits.isInitialized("lowPriorityDeliveryAndRetryProfile") ? new com.tango.sms.ws.model.common.QDeliveryAndRetryProfile(forProperty("lowPriorityDeliveryAndRetryProfile"), inits.get("lowPriorityDeliveryAndRetryProfile")) : null;
        this.regularPriorityDeliveryAndRetryProfile = inits.isInitialized("regularPriorityDeliveryAndRetryProfile") ? new com.tango.sms.ws.model.common.QDeliveryAndRetryProfile(forProperty("regularPriorityDeliveryAndRetryProfile"), inits.get("regularPriorityDeliveryAndRetryProfile")) : null;
        this.urgentPriorityDeliveryAndRetryProfile = inits.isInitialized("urgentPriorityDeliveryAndRetryProfile") ? new com.tango.sms.ws.model.common.QDeliveryAndRetryProfile(forProperty("urgentPriorityDeliveryAndRetryProfile"), inits.get("urgentPriorityDeliveryAndRetryProfile")) : null;
    }

}

