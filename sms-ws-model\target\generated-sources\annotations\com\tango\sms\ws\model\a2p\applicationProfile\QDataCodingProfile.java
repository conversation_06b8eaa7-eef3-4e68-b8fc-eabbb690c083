package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QDataCodingProfile is a Querydsl query type for DataCodingProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QDataCodingProfile extends BeanPath<DataCodingProfile> {

    private static final long serialVersionUID = 435156735L;

    public static final QDataCodingProfile dataCodingProfile = new QDataCodingProfile("dataCodingProfile");

    public final EnumPath<com.tango.sms.ws.model.enumeration.DataCodingStandard> defaultStandardForReceivedSms = createEnum("defaultStandardForReceivedSms", com.tango.sms.ws.model.enumeration.DataCodingStandard.class);

    public QDataCodingProfile(String variable) {
        super(DataCodingProfile.class, forVariable(variable));
    }

    public QDataCodingProfile(Path<? extends DataCodingProfile> path) {
        super(path.getType(), path.getMetadata());
    }

    public QDataCodingProfile(PathMetadata metadata) {
        super(DataCodingProfile.class, metadata);
    }

}

