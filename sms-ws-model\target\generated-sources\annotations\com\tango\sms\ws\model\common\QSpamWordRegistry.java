package com.tango.sms.ws.model.common;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QSpamWordRegistry is a Querydsl query type for SpamWordRegistry
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QSpamWordRegistry extends EntityPathBase<SpamWordRegistry> {

    private static final long serialVersionUID = 682392771L;

    public static final QSpamWordRegistry spamWordRegistry = new QSpamWordRegistry("spamWordRegistry");

    public final QBaseEntity _super = new QBaseEntity(this);

    public final BooleanPath caseSensitive = createBoolean("caseSensitive");

    public final BooleanPath countMultipleOccurences = createBoolean("countMultipleOccurences");

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final StringPath name = createString("name");

    public final ListPath<SpamWord, QSpamWord> spamWords = this.<SpamWord, QSpamWord>createList("spamWords", SpamWord.class, QSpamWord.class, PathInits.DIRECT2);

    public QSpamWordRegistry(String variable) {
        super(SpamWordRegistry.class, forVariable(variable));
    }

    public QSpamWordRegistry(Path<? extends SpamWordRegistry> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSpamWordRegistry(PathMetadata metadata) {
        super(SpamWordRegistry.class, metadata);
    }

}

