package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QReceiptProfile is a Querydsl query type for ReceiptProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QReceiptProfile extends BeanPath<ReceiptProfile> {

    private static final long serialVersionUID = 1690325223L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QReceiptProfile receiptProfile = new QReceiptProfile("receiptProfile");

    public final BooleanPath enabled = createBoolean("enabled");

    public final com.tango.sms.ws.model.common.QDeliveryAndRetryProfile receiptDeliveryAndRetryProfile;

    public final com.tango.sms.ws.model.common.QResourcePolicy receiptResourcePolicy;

    public QReceiptProfile(String variable) {
        this(ReceiptProfile.class, forVariable(variable), INITS);
    }

    public QReceiptProfile(Path<? extends ReceiptProfile> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QReceiptProfile(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QReceiptProfile(PathMetadata metadata, PathInits inits) {
        this(ReceiptProfile.class, metadata, inits);
    }

    public QReceiptProfile(Class<? extends ReceiptProfile> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.receiptDeliveryAndRetryProfile = inits.isInitialized("receiptDeliveryAndRetryProfile") ? new com.tango.sms.ws.model.common.QDeliveryAndRetryProfile(forProperty("receiptDeliveryAndRetryProfile"), inits.get("receiptDeliveryAndRetryProfile")) : null;
        this.receiptResourcePolicy = inits.isInitialized("receiptResourcePolicy") ? new com.tango.sms.ws.model.common.QResourcePolicy(forProperty("receiptResourcePolicy")) : null;
    }

}

