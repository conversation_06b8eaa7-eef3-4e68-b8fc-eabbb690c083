package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QNumberScreeningProfile is a Querydsl query type for NumberScreeningProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QNumberScreeningProfile extends BeanPath<NumberScreeningProfile> {

    private static final long serialVersionUID = 1343682770L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QNumberScreeningProfile numberScreeningProfile = new QNumberScreeningProfile("numberScreeningProfile");

    public final SetPath<com.tango.sms.ws.model.common.NumberList, com.tango.sms.ws.model.common.QNumberList> anumberBlackList = this.<com.tango.sms.ws.model.common.NumberList, com.tango.sms.ws.model.common.QNumberList>createSet("anumberBlackList", com.tango.sms.ws.model.common.NumberList.class, com.tango.sms.ws.model.common.QNumberList.class, PathInits.DIRECT2);

    public final SetPath<com.tango.sms.ws.model.common.NumberList, com.tango.sms.ws.model.common.QNumberList> anumberWhiteList = this.<com.tango.sms.ws.model.common.NumberList, com.tango.sms.ws.model.common.QNumberList>createSet("anumberWhiteList", com.tango.sms.ws.model.common.NumberList.class, com.tango.sms.ws.model.common.QNumberList.class, PathInits.DIRECT2);

    public final com.tango.sms.ws.model.common.QNumberList bnumberBlackList;

    public final com.tango.sms.ws.model.common.QNumberList bnumberWhiteList;

    public final BooleanPath enabled = createBoolean("enabled");

    public final com.tango.sms.ws.model.common.QNumberList vlrBlackList;

    public final com.tango.sms.ws.model.common.QNumberList vlrWhiteList;

    public QNumberScreeningProfile(String variable) {
        this(NumberScreeningProfile.class, forVariable(variable), INITS);
    }

    public QNumberScreeningProfile(Path<? extends NumberScreeningProfile> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QNumberScreeningProfile(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QNumberScreeningProfile(PathMetadata metadata, PathInits inits) {
        this(NumberScreeningProfile.class, metadata, inits);
    }

    public QNumberScreeningProfile(Class<? extends NumberScreeningProfile> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.bnumberBlackList = inits.isInitialized("bnumberBlackList") ? new com.tango.sms.ws.model.common.QNumberList(forProperty("bnumberBlackList")) : null;
        this.bnumberWhiteList = inits.isInitialized("bnumberWhiteList") ? new com.tango.sms.ws.model.common.QNumberList(forProperty("bnumberWhiteList")) : null;
        this.vlrBlackList = inits.isInitialized("vlrBlackList") ? new com.tango.sms.ws.model.common.QNumberList(forProperty("vlrBlackList")) : null;
        this.vlrWhiteList = inits.isInitialized("vlrWhiteList") ? new com.tango.sms.ws.model.common.QNumberList(forProperty("vlrWhiteList")) : null;
    }

}

