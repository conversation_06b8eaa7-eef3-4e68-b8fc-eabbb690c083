/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

CREATE TABLE `additional_property` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `section` varchar(255) DEFAULT NULL,
  `value` varchar(255) NOT NULL,
  `connection_id` bigint(20) DEFAULT NULL,
  `application_profile_id` bigint(20) DEFAULT NULL,
  `application_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKrwvdd177tbbstex0j0r5l6xwe` (`connection_id`),
  KEY `FKnq6mm20owm4ul4g2o0bn38v0r` (`application_profile_id`),
  KEY `FK69kkdbxakr56ipuas7sy01gs2` (`application_id`),
  CONSTRAINT `FK69kkdbxakr56ipuas7sy01gs2` FOREIGN KEY (`application_id`) REFERENCES `application` (`id`),
  CONSTRAINT `FKnq6mm20owm4ul4g2o0bn38v0r` FOREIGN KEY (`application_profile_id`) REFERENCES `application_profile` (`id`),
  CONSTRAINT `FKrwvdd177tbbstex0j0r5l6xwe` FOREIGN KEY (`connection_id`) REFERENCES `connection` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `application_profile`
--
CREATE TABLE `application_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `blackouts_enabled` bit(1) DEFAULT NULL,
  `cdrs_aux_services_cdr` bit(1) DEFAULT NULL,
  `cdrs_charging_cdr` bit(1) DEFAULT NULL,
  `cdrs_enabled` bit(1) DEFAULT NULL,
  `cdrs_final_cdr` bit(1) DEFAULT NULL,
  `cdrs_receipt_cdr` bit(1) DEFAULT NULL,
  `cdrs_retry_cdr` bit(1) DEFAULT NULL,
  `cdrs_submit_cdr` bit(1) DEFAULT NULL,
  `charging_strategy` varchar(255) DEFAULT NULL,
  `charging_enabled` bit(1) DEFAULT NULL,
  `charging_originating_subscriber` bit(1) DEFAULT NULL,
  `content_screening_enabled` bit(1) DEFAULT NULL,
  `content_screening_token_threshold` bigint(20) DEFAULT NULL,
  `data_coding_default_standard_for_delivery_of_sms` varchar(255) DEFAULT NULL,
  `data_coding_default_standard_for_received_sms` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `retries_default_priority` varchar(255) DEFAULT NULL,
  `retries_override_message_priority` bit(1) DEFAULT NULL,
  `retries_override_priority` varchar(255) DEFAULT NULL,
  `store_enable_message_reassembly` bit(1) DEFAULT NULL,
  `store_enabled` bit(1) DEFAULT NULL,
  `throttling_analysis_window_in_seconds` bigint(20) DEFAULT NULL,
  `throttling_analysis_window_sms_threshold` bigint(20) DEFAULT NULL,
  `throttling_analysis_window_threshold` bigint(20) DEFAULT NULL,
  `throttling_enabled` bit(1) DEFAULT NULL,
  `throttling_number_of_windows_to_analyse` bigint(20) DEFAULT NULL,
  `content_screening_spam_word_registry_id` bigint(20) DEFAULT NULL,
  `retries_emergency_priority_delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,
  `retries_low_priority_delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,
  `retries_regular_priority_delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,
  `retries_urgent_priority_delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,
  `consumer_resource_policy_id` bigint(20) DEFAULT NULL,
  `store_resource_policy_id` bigint(20) DEFAULT NULL,  
  `receipts_enabled` bit(1) DEFAULT NULL, 
  `receipt_resource_policy_id` bigint(20) DEFAULT NULL,
  `receipt_delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,  
  `receipt_store_enabled` bit(1) DEFAULT NULL,
  `receipt_store_consumer_resource_policy_id` bigint(20) DEFAULT NULL,
  `receipt_store_resource_policy_id` bigint(20) DEFAULT NULL,
  `receipt_store_enable_message_reassembly` bit(1) DEFAULT NULL,
  `number_screening_enabled` bit(1) DEFAULT NULL,
  `number_screening_b_number_black_list_id` bigint(20) DEFAULT NULL,
  `number_screening_b_number_white_list_id` bigint(20) DEFAULT NULL,
  `number_screening_vlr_black_list_id` bigint(20) DEFAULT NULL,
  `number_screening_vlr_white_list_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `FKeb3lmi3j5tsfspuanbyl36oi` (`content_screening_spam_word_registry_id`),
  KEY `FKalt8h3pxjcemah1b46nhm3quc` (`retries_emergency_priority_delivery_and_retry_profile_id`),
  KEY `FKjuaon7je4bkocol7ckv501wer` (`retries_low_priority_delivery_and_retry_profile_id`),
  KEY `FKs4nmvanjfbb344v76oav5s8oc` (`retries_regular_priority_delivery_and_retry_profile_id`),
  KEY `FK9ro6wu3nsrse3ycoxl2976b8a` (`retries_urgent_priority_delivery_and_retry_profile_id`),
  KEY `FKcijj4lhx9uhce54ikfeq7ni57` (`receipt_resource_policy_id`),
  KEY `FKi1bw961jvvk47yvoxkqjw6wll` (`receipt_delivery_and_retry_profile_id`),
  KEY `FK8kxpgf1o5yigxdfudwrnsrf83` (`consumer_resource_policy_id`),
  KEY `FKjykat0xcphxtncx2f10beqn00` (`store_resource_policy_id`),
  KEY `FK8kxpgf1o5yigxdfudwrnsrf81` (`receipt_store_consumer_resource_policy_id`),
  KEY `FKjykat0xcphxtncx2f10beqn04` (`receipt_store_resource_policy_id`),
  KEY `FK334nkrtgg7hv5h4x6ivwgko54` (`number_screening_b_number_black_list_id`),
  KEY `FKmxfsenf9lai3qi2srfk9tqk2k` (`number_screening_b_number_white_list_id`),
  KEY `FKn9fj1yvrye0je7y6xd9x9exd6` (`number_screening_vlr_black_list_id`),
  KEY `FKcjvbhaay2ulbhpla0chyclegn` (`number_screening_vlr_white_list_id`),
  CONSTRAINT `FK334nkrtgg7hv5h4x6ivwgko54` FOREIGN KEY (`number_screening_b_number_black_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FK8kxpgf1o5yigxdfudwrnsrf83` FOREIGN KEY (`consumer_resource_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FK8kxpgf1o5yigxdfudwrnsrf81` FOREIGN KEY (`receipt_store_consumer_resource_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FK9ro6wu3nsrse3ycoxl2976b8a` FOREIGN KEY (`retries_urgent_priority_delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`),
  CONSTRAINT `FKalt8h3pxjcemah1b46nhm3quc` FOREIGN KEY (`retries_emergency_priority_delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`),
  CONSTRAINT `FKcijj4lhx9uhce54ikfeq7ni57` FOREIGN KEY (`receipt_resource_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FKcjvbhaay2ulbhpla0chyclegn` FOREIGN KEY (`number_screening_vlr_white_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FKeb3lmi3j5tsfspuanbyl36oi` FOREIGN KEY (`content_screening_spam_word_registry_id`) REFERENCES `spam_word_registry` (`id`),
  CONSTRAINT `FKi1bw961jvvk47yvoxkqjw6wll` FOREIGN KEY (`receipt_delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`),
  CONSTRAINT `FKjuaon7je4bkocol7ckv501wer` FOREIGN KEY (`retries_low_priority_delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`),
  CONSTRAINT `FKjykat0xcphxtncx2f10beqn00` FOREIGN KEY (`store_resource_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FKjykat0xcphxtncx2f10beqn04` FOREIGN KEY (`receipt_store_resource_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FKmxfsenf9lai3qi2srfk9tqk2k` FOREIGN KEY (`number_screening_b_number_white_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FKn9fj1yvrye0je7y6xd9x9exd6` FOREIGN KEY (`number_screening_vlr_black_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FKs4nmvanjfbb344v76oav5s8oc` FOREIGN KEY (`retries_regular_priority_delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;


--
-- Populate `application_profile` with data from application
--
INSERT into application_profile(name, 
content_screening_enabled, content_screening_token_threshold, content_screening_spam_word_registry_id, 
throttling_enabled,throttling_analysis_window_in_seconds,throttling_analysis_window_sms_threshold,throttling_number_of_windows_to_analyse,throttling_analysis_window_threshold,
number_screening_b_number_black_list_id,number_screening_b_number_white_list_id,number_screening_vlr_black_list_id,number_screening_vlr_white_list_id) 
SELECT short_code,true,token_threshold,spam_word_registry_id,
true,analysis_window_in_seconds,analysis_window_sms_threshold,number_of_windows_to_analyse,analysis_window_threshold,
b_number_black_list_id,b_number_white_list_id,vlr_black_list_id,vlr_white_list_id
FROM application LEFT JOIN (content_screening_profile, number_screening_profile, throttling_profile) ON 
(content_screening_profile.id = application.content_screening_profile_id AND number_screening_profile.id = application.number_screening_profile_id AND throttling_profile.id = application.throttling_profile_id);

--
-- Point application to application profile
--
ALTER TABLE application ADD `application_profile_id` bigint(20) DEFAULT NULL;
--
-- Set application profile name based on 
--
UPDATE application AS a INNER JOIN application_profile AS b ON a.short_code=b.name SET a.application_profile_id= b.id;

--
-- Point blackout periods to application profile rather than application
--
ALTER TABLE blackout_period ADD `application_profile_id` bigint(20) DEFAULT NULL;
UPDATE blackout_period as bper 
LEFT JOIN blackout_profile bpr ON bpr.id=bper.blackout_profile_id
LEFT JOIN application app ON bpr.id=app.blackout_profile_id
LEFT JOIN application_profile apr ON app.application_profile_id = apr.id
SET bper.application_profile_id=apr.id;

--
-- Point blacklists to application profile instead of application
--
CREATE TABLE `application_profile_anumber_blacklist` (
  `application_profile_id` bigint(20) NOT NULL,
  `number_list_id` bigint(20) NOT NULL,
  PRIMARY KEY (`application_profile_id`,`number_list_id`),
  KEY `FKpdx16tx9xt8dqhkrccai1clx7` (`number_list_id`),
  CONSTRAINT `FK4k5cnycyxmvrdq1lkevwc1c1s` FOREIGN KEY (`application_profile_id`) REFERENCES `application_profile` (`id`),
  CONSTRAINT `FKpdx16tx9xt8dqhkrccai1clx7` FOREIGN KEY (`number_list_id`) REFERENCES `number_list` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT into application_profile_anumber_blacklist (application_profile_id, number_list_id)
SELECT apr.id as application_profile_id, sp.number_list_id as number_list_id
FROM screening_profile_anumber_blacklist sp
LEFT JOIN number_screening_profile nsp ON nsp.id=sp.number_screening_profile_id
LEFT JOIN application app ON nsp.id=app.number_screening_profile_id
LEFT JOIN application_profile apr ON app.application_profile_id = apr.id;

--
-- Point blacklists to application profile instead of application
--
CREATE TABLE `application_profile_anumber_whitelist` (
  `application_profile_id` bigint(20) NOT NULL,
  `number_list_id` bigint(20) NOT NULL,
  PRIMARY KEY (`application_profile_id`,`number_list_id`),
  KEY `FK5o4krsitoublgc9lq0gbthgtp` (`number_list_id`),
  CONSTRAINT `FK5o4krsitoublgc9lq0gbthgtp` FOREIGN KEY (`number_list_id`) REFERENCES `number_list` (`id`),
  CONSTRAINT `FKp8paylokkmamau5kam5avmtc0` FOREIGN KEY (`application_profile_id`) REFERENCES `application_profile` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

INSERT into application_profile_anumber_whitelist (application_profile_id, number_list_id)
SELECT apr.id as application_profile_id, sp.number_list_id as number_list_id
FROM screening_profile_anumber_whitelist sp
LEFT JOIN number_screening_profile nsp ON nsp.id=sp.number_screening_profile_id
LEFT JOIN application app ON nsp.id=app.number_screening_profile_id
LEFT JOIN application_profile apr ON app.application_profile_id = apr.id;

ALTER TABLE application DROP FOREIGN KEY FK9jog2fyxky9r1hs6g8y29gax1;
ALTER TABLE application DROP COLUMN content_screening_profile_id;

ALTER TABLE application DROP COLUMN application_status;
ALTER TABLE application DROP COLUMN short_code;
ALTER TABLE application DROP COLUMN ton_npi;

ALTER TABLE application DROP FOREIGN KEY FKdbc4apd8fhy9lm2qctovyei8x;
ALTER TABLE application DROP COLUMN throttling_profile_id;

ALTER TABLE application DROP FOREIGN KEY FKhk88y2he258nq91ankb0np8f9;
ALTER TABLE application DROP COLUMN number_screening_profile_id;  

ALTER TABLE application DROP FOREIGN KEY FK1ry55clu0eowpl0il5oix1wmo;
ALTER TABLE application DROP COLUMN blackout_profile_id;

ALTER TABLE application DROP FOREIGN KEY FKhohm38iyfump694eqy4h3nmol;
ALTER TABLE application CHANGE connection_id p2a_connection_id bigint(20) DEFAULT NULL;
ALTER TABLE application ADD CONSTRAINT `FKhohm38iyfump694eqy4h3nmol` FOREIGN KEY (`p2a_connection_id`) REFERENCES `connection` (`id`);

ALTER TABLE application ADD `a2p_message_handling_enabled` bit(1) DEFAULT NULL;
ALTER TABLE application ADD `name` varchar(255) DEFAULT NULL;
ALTER TABLE application ADD `p2a_message_handling_enabled` bit(1) DEFAULT NULL;
ALTER TABLE application ADD `replacement_source_address` varchar(255) DEFAULT NULL;
ALTER TABLE application ADD `network_address_id` bigint(20) DEFAULT NULL;
ALTER TABLE application ADD CONSTRAINT `FK3snl228hf20k2m8nrw09iwqoa` FOREIGN KEY (`application_profile_id`) REFERENCES `application_profile` (`id`);
ALTER TABLE application ADD CONSTRAINT `FKpy3mi7hyubum73nlhulwb52wk` FOREIGN KEY (`network_address_id`) REFERENCES `network_address` (`id`);
ALTER TABLE application ADD UNIQUE (name);

CREATE TABLE `b_number_prefix_override` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `prefix` varchar(255) DEFAULT NULL,
  `b_number_length` int(11) DEFAULT NULL,
  `mo_routing_class` bigint(20) DEFAULT NULL,
  `mo_routing_table_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK5o4awfb6v8puj4196q8pqgdkm` (`mo_routing_class`),
  KEY `FKoopa5f3fw06am6xpvavi1b68j` (`mo_routing_table_id`),
  CONSTRAINT `FK5o4awfb6v8puj4196q8pqgdkm` FOREIGN KEY (`mo_routing_class`) REFERENCES `mo_routing_class` (`id`),
  CONSTRAINT `FKoopa5f3fw06am6xpvavi1b68j` FOREIGN KEY (`mo_routing_table_id`) REFERENCES `mo_routing_table` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8;


ALTER TABLE blackout_period DROP FOREIGN KEY FK18q5s4eqacmrbbe9nrhe7f59c;
ALTER TABLE blackout_period DROP COLUMN blackout_profile_id;
ALTER TABLE blackout_period ADD CONSTRAINT `FKhqq3p23d9xd9ijtcbvbp1ie3` FOREIGN KEY (`application_profile_id`) REFERENCES `application_profile` (`id`);
ALTER TABLE blackout_period CHANGE entity_name name varchar(255) DEFAULT NULL;
ALTER TABLE blackout_duration DROP COLUMN dow_selector;
ALTER TABLE blackout_duration ADD `days_active` varchar(255) DEFAULT NULL;

DROP TABLE IF EXISTS `blackout_profile`;

ALTER TABLE connection ADD `application_selection_strategy` varchar(255) DEFAULT NULL;
ALTER TABLE connection ADD `connection_monitoring_strategy` varchar(255) DEFAULT NULL;
ALTER TABLE connection ADD `ip_allowlist_enabled` bit(1) DEFAULT NULL;
ALTER TABLE connection ADD `max_binds` int(11) DEFAULT NULL;
ALTER TABLE connection ADD `name` varchar(255) DEFAULT NULL;
ALTER TABLE connection ADD `password` varchar(255) DEFAULT NULL;
ALTER TABLE connection ADD `traffic_activity_monitoring_error_threshold` int(11) DEFAULT NULL;
ALTER TABLE connection ADD `traffic_activity_monitoring_window_seconds` int(11) DEFAULT NULL;
ALTER TABLE connection ADD `application_id` bigint(20) DEFAULT NULL;
ALTER TABLE connection ADD `ip_allowlist_id` bigint(20) DEFAULT NULL;
ALTER TABLE connection ADD CONSTRAINT `FKob1bkxsccu3d380b9q01wn2sl` FOREIGN KEY (`application_id`) REFERENCES `application` (`id`);
ALTER TABLE connection ADD CONSTRAINT `FKu7bqq4ahjand3nx8c1own901` FOREIGN KEY (`ip_allowlist_id`) REFERENCES `ip_address_list` (`id`);
ALTER TABLE connection ADD UNIQUE (name);

DROP TABLE IF EXISTS `content_screening_profile`;

--
-- Table structure for table `ip_address_entry`
--
CREATE TABLE `ip_address_entry` (
  `ip_address_list_id` bigint(20) NOT NULL,
  `ip_address` varchar(255) NOT NULL,
  KEY `FKj68rmrrowvqkyxk3fwqc1nc6s` (`ip_address_list_id`),
  CONSTRAINT `FKj68rmrrowvqkyxk3fwqc1nc6s` FOREIGN KEY (`ip_address_list_id`) REFERENCES `ip_address_list` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `ip_address_list`
--
CREATE TABLE `ip_address_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8;

--
-- Table structure for table `mo_route`
--
CREATE TABLE `mo_route` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `aux_service_enabled` bit(1) DEFAULT NULL,
  `priority` varchar(255) DEFAULT NULL,
  `response_timeout` int(11) DEFAULT NULL,
  `route_order` int(11) DEFAULT NULL,
  `routing_interface` varchar(255) DEFAULT NULL,
  `resource_policy_id` bigint(20) DEFAULT NULL,
  `mo_routing_class_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKtfn6osvjn23bbiu5jp058xkeq` (`resource_policy_id`),
  KEY `FK30f5yaqjxmoou0dubw14pdmcp` (`mo_routing_class_id`),
  CONSTRAINT `FK30f5yaqjxmoou0dubw14pdmcp` FOREIGN KEY (`mo_routing_class_id`) REFERENCES `mo_routing_class` (`id`),
  CONSTRAINT `FKtfn6osvjn23bbiu5jp058xkeq` FOREIGN KEY (`resource_policy_id`) REFERENCES `resource_policy` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8;

--
-- Table structure for table `mo_routing_class`
--
CREATE TABLE `mo_routing_class` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `aux_service_local_forwarding` bit(1) DEFAULT NULL,
  `aux_service_response_timeout` int(11) DEFAULT NULL,
  `mt_protocol` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `aux_service_broker_resource_policy_id` bigint(20) DEFAULT NULL,
  `consumer_policy_id` bigint(20) DEFAULT NULL,
  `interworking_storage_policy_id` bigint(20) DEFAULT NULL,
  `delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `FK5cd93iqxnykh8j0gx1391u84j` (`aux_service_broker_resource_policy_id`),
  KEY `FKrvbkr2ehutait0nvt2feltutg` (`consumer_policy_id`),
  KEY `FKjfi8j8od7wajsoji3sdt1gxxu` (`interworking_storage_policy_id`),
  KEY `FK5dccv28m7k1y879crmo1e3juv` (`delivery_and_retry_profile_id`),
  CONSTRAINT `FK5cd93iqxnykh8j0gx1391u84j` FOREIGN KEY (`aux_service_broker_resource_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FK5dccv28m7k1y879crmo1e3juv` FOREIGN KEY (`delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`),
  CONSTRAINT `FKjfi8j8od7wajsoji3sdt1gxxu` FOREIGN KEY (`interworking_storage_policy_id`) REFERENCES `resource_policy` (`id`),
  CONSTRAINT `FKrvbkr2ehutait0nvt2feltutg` FOREIGN KEY (`consumer_policy_id`) REFERENCES `resource_policy` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

--
-- Table structure for table `mo_routing_table`
--
CREATE TABLE `mo_routing_table` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `p2a_default_routing_class` bigint(20) DEFAULT NULL,
  `p2p_default_routing_class` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `FKrxbnfyjs0sk3as195vfjebjqb` (`p2a_default_routing_class`),
  KEY `FK9pmxctcns8lxp4fq0b4nee065` (`p2p_default_routing_class`),
  CONSTRAINT `FK9pmxctcns8lxp4fq0b4nee065` FOREIGN KEY (`p2p_default_routing_class`) REFERENCES `mo_routing_class` (`id`),
  CONSTRAINT `FKrxbnfyjs0sk3as195vfjebjqb` FOREIGN KEY (`p2a_default_routing_class`) REFERENCES `mo_routing_class` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;


--
-- Table structure for table `network_address`
--
CREATE TABLE `network_address` (
  `type` varchar(31) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `network_band` varchar(255) DEFAULT NULL,
  `type_of_number` varchar(255) DEFAULT NULL,
  `number_plan_indicator` varchar(255) DEFAULT NULL,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `short_code` varchar(255) DEFAULT NULL,
  `short_code_length` varchar(255) DEFAULT NULL,
  `short_code_prefix` int(11) DEFAULT NULL,
  `short_code_from` varchar(255) DEFAULT NULL,
  `short_code_to` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;


--
-- Table structure for table `number_entry`
--
ALTER TABLE number_entry ADD `number_list_order` int(11) DEFAULT NULL;

--
-- Table structure for table `number_list`
--
ALTER TABLE number_list MODIFY number_list_type VARCHAR(255);

DROP TABLE IF EXISTS `number_screening_profile`;

--
-- Table structure for table `resource_policy`
--
CREATE TABLE `resource_policy` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `retry_error_schedule_map_entry`
--

CREATE TABLE `delivery_error_schedule_map_entry` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `map_error_code` varchar(255) DEFAULT NULL,
  `delivery_and_retry_schedule_id` bigint(20) DEFAULT NULL,
  `delivery_and_retry_profile_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKk3u79v5j4qfr0bhvqrpixw089` (`delivery_and_retry_schedule_id`),
  KEY `FKha9ubsvounxojyopre731tivi` (`delivery_and_retry_profile_id`),
  CONSTRAINT `FKha9ubsvounxojyopre731tivi` FOREIGN KEY (`delivery_and_retry_profile_id`) REFERENCES `delivery_and_retry_profile` (`id`),
  CONSTRAINT `FKk3u79v5j4qfr0bhvqrpixw089` FOREIGN KEY (`delivery_and_retry_schedule_id`) REFERENCES `delivery_and_retry_schedule` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8;

--
-- Table structure for table `delivery_and_retry_profile`
--
CREATE TABLE `delivery_and_retry_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `delay_before_first_delivery` bigint(20) DEFAULT NULL,
  `max_expiry_time` bigint(20) DEFAULT NULL,
  `max_delivery_attempts` bigint(20) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `validity_period_enabled` bit(1) DEFAULT NULL,
  `default_delivery_and_retry_schedule_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`),
  KEY `FKraly35ul9l43wk4btjwqy8bcp` (`default_delivery_and_retry_schedule_id`),
  CONSTRAINT `FKraly35ul9l43wk4btjwqy8bcp` FOREIGN KEY (`default_delivery_and_retry_schedule_id`) REFERENCES `delivery_and_retry_schedule` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8;

--
-- Table structure for table `delivery_and_retry_schedule`
--
CREATE TABLE `delivery_and_retry_schedule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8;

--
-- Table structure for table `delivery_and_retry_schedule_state`
--
CREATE TABLE `delivery_and_retry_schedule_state` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `delay_after_failure` bigint(20) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `next_state` varchar(255) DEFAULT NULL,
  `sri_retry_priority_enabled` bit(1) DEFAULT NULL,
  `delivery_and_retry_schedule_id` bigint(20) DEFAULT NULL,
  `state_order` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKefqt3nb0ryrqd8nbjql8c27hu` (`delivery_and_retry_schedule_id`),
  CONSTRAINT `FKefqt3nb0ryrqd8nbjql8c27hu` FOREIGN KEY (`delivery_and_retry_schedule_id`) REFERENCES `delivery_and_retry_schedule` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8;

--
-- Table structure for table `delivery_and_retry_schedule_state_exception`
--
CREATE TABLE `delivery_and_retry_schedule_state_exception` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime(6) DEFAULT NULL,
  `updated_time` datetime(6) DEFAULT NULL,
  `action` varchar(255) NOT NULL,
  `delay_after_failure` bigint(20) DEFAULT NULL,
  `map_error_code` varchar(255) NOT NULL,
  `next_state` varchar(255) DEFAULT NULL,
  `delivery_and_retry_schedule_state_id` bigint(20) DEFAULT NULL,
  `exception_order` int(11) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKp0wbtxgspby8u9cssqui1f1n6` (`delivery_and_retry_schedule_state_id`),
  CONSTRAINT `FKp0wbtxgspby8u9cssqui1f1n6` FOREIGN KEY (`delivery_and_retry_schedule_state_id`) REFERENCES `delivery_and_retry_schedule_state` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Table structure for table `screening_profile` unchanged
--

DROP TABLE IF EXISTS `screening_profile_anumber_blacklist`;

DROP TABLE IF EXISTS `screening_profile_anumber_whitelist`;

--
-- Table structure for table `screening_profile_join_number_list` unchanged
--

--
-- Table structure for table `spam_word` unchanged
--

--
-- Table structure for table `spam_word_registry`
--
ALTER TABLE spam_word_registry ADD UNIQUE (name);


DROP TABLE IF EXISTS `throttling_profile`;

/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
