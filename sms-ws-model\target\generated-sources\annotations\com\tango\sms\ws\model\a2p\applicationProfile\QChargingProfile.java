package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QChargingProfile is a Querydsl query type for ChargingProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QChargingProfile extends BeanPath<ChargingProfile> {

    private static final long serialVersionUID = 518279202L;

    public static final QChargingProfile chargingProfile = new QChargingProfile("chargingProfile");

    public final EnumPath<com.tango.sms.ws.model.enumeration.ChargingStrategy> chargingStrategy = createEnum("chargingStrategy", com.tango.sms.ws.model.enumeration.ChargingStrategy.class);

    public final BooleanPath enabled = createBoolean("enabled");

    public final BooleanPath originatingSubscriber = createBoolean("originatingSubscriber");

    public QChargingProfile(String variable) {
        super(ChargingProfile.class, forVariable(variable));
    }

    public QChargingProfile(Path<? extends ChargingProfile> path) {
        super(path.getType(), path.getMetadata());
    }

    public QChargingProfile(PathMetadata metadata) {
        super(ChargingProfile.class, metadata);
    }

}

