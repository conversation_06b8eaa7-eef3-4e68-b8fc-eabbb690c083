
	
RENAME TABLE prefix_list TO number_list;
ALTER TABLE number_list ADD `number_list_type` varchar(255) NOT NULL;
UPDATE number_list set `number_list_type` = 'PREFIX_LIST';

RENAME TABLE prefix TO number_entry;
ALTER TABLE number_entry CHANGE `prefix` `number_from` varchar(255) NOT NULL;

ALTER TABLE number_entry DROP FOREIGN KEY FKf2l1etnoqloea3u7d4hbswyt1,
	CHANGE `prefix_list_id` `number_list_id` bigint(20) DEFAULT NULL,
	ADD CONSTRAINT `fk_number_entry_to_number_list` FOREIGN KEY (`number_list_id`) REFERENCES `number_list` (`id`);

ALTER TABLE number_entry ADD `number_to` varchar(255) DEFAULT NULL;
ALTER TABLE number_entry ADD `number_type` varchar(255) DEFAULT 'DEFAULT';
UPDATE number_entry SET number_type = 'INTERNATIONAL' WHERE number_from LIKE '+%';
UPDATE number_entry SET number_from = REPLACE(number_from, '+', '');

RENAME TABLE screening_profile_join_prefix_list TO screening_profile_join_number_list;
ALTER TABLE screening_profile_join_number_list DROP FOREIGN KEY FKoow0qq2rouhs2rssjod4eh3at,
	CHANGE `prefix_list_id` `number_list_id` bigint(20),
	ADD CONSTRAINT `fk_spjnl_to_number_list` FOREIGN KEY (`number_list_id`) REFERENCES `number_list` (`id`);
	
CREATE TABLE `screening_profile_anumber_blacklist` (
  `number_screening_profile_id` bigint(20) NOT NULL,
  `number_list_id` bigint(20) NOT NULL,
  PRIMARY KEY (`number_screening_profile_id`,`number_list_id`),
  KEY `FK25cgarg6xhfhgtid9j3key458` (`number_list_id`),
  CONSTRAINT `FKmmq56vdb3ksqfyfncuqvlpf8i` FOREIGN KEY (`number_screening_profile_id`) REFERENCES `number_screening_profile` (`id`),
  CONSTRAINT `FK25cgarg6xhfhgtid9j3key458` FOREIGN KEY (`number_list_id`) REFERENCES `number_list` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

CREATE TABLE `screening_profile_anumber_whitelist` (
  `number_screening_profile_id` bigint(20) NOT NULL,
  `number_list_id` bigint(20) NOT NULL,
  PRIMARY KEY (`number_screening_profile_id`,`number_list_id`),
  KEY `FK1h7r18xc7k8dhkqdt95kdl9ic` (`number_list_id`),
  CONSTRAINT `FK9ppm0nwj12reo7gxmv0lvboe8` FOREIGN KEY (`number_screening_profile_id`) REFERENCES `number_screening_profile` (`id`),
  CONSTRAINT `FK1h7r18xc7k8dhkqdt95kdl9ic` FOREIGN KEY (`number_list_id`) REFERENCES `number_list` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;


	