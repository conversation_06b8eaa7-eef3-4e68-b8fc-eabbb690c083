-- MySQL dump 10.13  Distrib 5.5.35, for Linux (x86_64)
--
-- Host: localhost    Database: smswsdb
-- ------------------------------------------------------
-- Server version	5.5.35

/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8 */;
/*!40103 SET @OLD_TIME_ZONE=@@TIME_ZONE */;
/*!40103 SET TIME_ZONE='+00:00' */;
/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;

--
-- Current Database: `smswsdb`
--

CREATE DATABASE /*!32312 IF NOT EXISTS*/ `smswsdb` /*!40100 DEFAULT CHARACTER SET latin1 */;

USE `smswsdb`;

DROP TABLE IF EXISTS `connection_join_application_list`;
DROP TABLE IF EXISTS `application_join_blackout_profile_list`;
DROP TABLE IF EXISTS `blackout_profile_join_blackout_period_list`;
DROP TABLE IF EXISTS `content_screening_profile_join_spam_word_registry_list`;

--
-- Table structure for table `analysis_profile`
--

DROP TABLE IF EXISTS `analysis_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `analysis_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `b_number_allowed_hex_characters` varchar(255) DEFAULT NULL,
  `b_number_comparison_enabled` bit(1) NOT NULL,
  `b_number_logcdr` bit(1) DEFAULT NULL,
  `b_number_log_event` bit(1) DEFAULT NULL,
  `b_number_lower_bound` int(11) DEFAULT NULL,
  `b_numbersmsto_compare` int(11) DEFAULT NULL,
  `b_number_upper_bound` int(11) DEFAULT NULL,
  `b_number_virus_check_enabled` bit(1) NOT NULL,
  `b_number_virus_logcdr` bit(1) DEFAULT NULL,
  `b_number_virus_log_event` bit(1) DEFAULT NULL,
  `b_number_virus_smpp_logging` bit(1) DEFAULT NULL,
  `blocked_reporting_period` int(11) NOT NULL,
  `content_length_and_text_check_enabled` bit(1) NOT NULL,
  `content_length_and_text_check_number_ofsms` int(11) DEFAULT NULL,
  `content_length_and_text_logcdr` bit(1) DEFAULT NULL,
  `content_length_and_text_log_event` bit(1) DEFAULT NULL,
  `content_spam_check_enabled` bit(1) NOT NULL,
  `length_check_lower_bound` int(11) DEFAULT NULL,
  `length_check_upper_bound` int(11) DEFAULT NULL,
  `max_sms_to_analyse` int(11) NOT NULL,
  `name` varchar(255) DEFAULT NULL,
  `rate_enabled` bit(1) NOT NULL,
  `rate_logcdr` bit(1) DEFAULT NULL,
  `rate_log_event` bit(1) DEFAULT NULL,
  `ratesmslower_bound` int(11) DEFAULT NULL,
  `ratesmsupper_bound` int(11) DEFAULT NULL,
  `rate_window` int(11) DEFAULT NULL,
  `sms_to_analyse_window` int(11) NOT NULL,
  `spam_logcdr` bit(1) DEFAULT NULL,
  `spam_log_cdr_content` bit(1) DEFAULT NULL,
  `spam_log_event` bit(1) DEFAULT NULL,
  `spam_lower_bound` int(11) DEFAULT NULL,
  `spam_upper_bound` int(11) DEFAULT NULL,
  `suspect_block_period` int(11) NOT NULL,
  `text_check_lower_bound` int(11) DEFAULT NULL,
  `text_check_upper_bound` int(11) DEFAULT NULL,
  `b_number_virus_check_prefix_list_id` bigint(20) DEFAULT NULL,
  `spam_word_registry_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK3lac5dt23jh4vihq2ebg0iy1f` (`b_number_virus_check_prefix_list_id`),
  KEY `FK5mqtcq6q5dvj89gj6ymrmjejs` (`spam_word_registry_id`),
  CONSTRAINT `FK5mqtcq6q5dvj89gj6ymrmjejs` FOREIGN KEY (`spam_word_registry_id`) REFERENCES `spam_word_registry` (`id`),
  CONSTRAINT `FK3lac5dt23jh4vihq2ebg0iy1f` FOREIGN KEY (`b_number_virus_check_prefix_list_id`) REFERENCES `prefix_list` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `analysis_profile`
--

LOCK TABLES `analysis_profile` WRITE;
/*!40000 ALTER TABLE `analysis_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `analysis_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `analysis_profile_rule`
--

DROP TABLE IF EXISTS `analysis_profile_rule`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `analysis_profile_rule` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `analysis_profile_rule_type` varchar(255) NOT NULL,
  `enabled` bit(1) NOT NULL,
  `value` varchar(255) DEFAULT NULL,
  `analysis_profile_rules_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKsrgplu426nf682430lx75a8wa` (`analysis_profile_rules_id`),
  CONSTRAINT `FKsrgplu426nf682430lx75a8wa` FOREIGN KEY (`analysis_profile_rules_id`) REFERENCES `analysis_profile_rules` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `analysis_profile_rule`
--

LOCK TABLES `analysis_profile_rule` WRITE;
/*!40000 ALTER TABLE `analysis_profile_rule` DISABLE KEYS */;
/*!40000 ALTER TABLE `analysis_profile_rule` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `analysis_profile_rules`
--

DROP TABLE IF EXISTS `analysis_profile_rules`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `analysis_profile_rules` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `threshold` varchar(255) NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `analysis_profile_rules`
--

LOCK TABLES `analysis_profile_rules` WRITE;
/*!40000 ALTER TABLE `analysis_profile_rules` DISABLE KEYS */;
/*!40000 ALTER TABLE `analysis_profile_rules` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `application`
--

DROP TABLE IF EXISTS `application`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `application` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `application_status` varchar(255) NOT NULL,
  `short_code` varchar(255) DEFAULT NULL,
  `ton_npi` varchar(255) DEFAULT NULL,
  `blackout_profile_id` bigint(20) DEFAULT NULL,
  `content_screening_profile_id` bigint(20) DEFAULT NULL,
  `number_screening_profile_id` bigint(20) DEFAULT NULL,
  `throttling_profile_id` bigint(20) DEFAULT NULL,
  `connection_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK1ry55clu0eowpl0il5oix1wmo` (`blackout_profile_id`),
  KEY `FK9jog2fyxky9r1hs6g8y29gax1` (`content_screening_profile_id`),
  KEY `FKhk88y2he258nq91ankb0np8f9` (`number_screening_profile_id`),
  KEY `FKdbc4apd8fhy9lm2qctovyei8x` (`throttling_profile_id`),
  KEY `FKhohm38iyfump694eqy4h3nmol` (`connection_id`),
  CONSTRAINT `FKhohm38iyfump694eqy4h3nmol` FOREIGN KEY (`connection_id`) REFERENCES `connection` (`id`),
  CONSTRAINT `FK1ry55clu0eowpl0il5oix1wmo` FOREIGN KEY (`blackout_profile_id`) REFERENCES `blackout_profile` (`id`),
  CONSTRAINT `FK9jog2fyxky9r1hs6g8y29gax1` FOREIGN KEY (`content_screening_profile_id`) REFERENCES `content_screening_profile` (`id`),
  CONSTRAINT `FKdbc4apd8fhy9lm2qctovyei8x` FOREIGN KEY (`throttling_profile_id`) REFERENCES `throttling_profile` (`id`),
  CONSTRAINT `FKhk88y2he258nq91ankb0np8f9` FOREIGN KEY (`number_screening_profile_id`) REFERENCES `number_screening_profile` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `application`
--

LOCK TABLES `application` WRITE;
/*!40000 ALTER TABLE `application` DISABLE KEYS */;
/*!40000 ALTER TABLE `application` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blackout_duration`
--

DROP TABLE IF EXISTS `blackout_duration`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blackout_duration` (
  `type` varchar(31) NOT NULL,
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `end_time` time DEFAULT NULL,
  `start_time` time DEFAULT NULL,
  `dow_selector` smallint(6) DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blackout_duration`
--

LOCK TABLES `blackout_duration` WRITE;
/*!40000 ALTER TABLE `blackout_duration` DISABLE KEYS */;
/*!40000 ALTER TABLE `blackout_duration` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blackout_period`
--

DROP TABLE IF EXISTS `blackout_period`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blackout_period` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `entity_name` varchar(255) DEFAULT NULL,
  `duration_id` bigint(20) DEFAULT NULL,
  `blackout_profile_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FK9cdrh8pp9pwgsedjj3pwyk9w2` (`duration_id`),
  KEY `FK18q5s4eqacmrbbe9nrhe7f59c` (`blackout_profile_id`),
  CONSTRAINT `FK18q5s4eqacmrbbe9nrhe7f59c` FOREIGN KEY (`blackout_profile_id`) REFERENCES `blackout_profile` (`id`),
  CONSTRAINT `FK9cdrh8pp9pwgsedjj3pwyk9w2` FOREIGN KEY (`duration_id`) REFERENCES `blackout_duration` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blackout_period`
--

LOCK TABLES `blackout_period` WRITE;
/*!40000 ALTER TABLE `blackout_period` DISABLE KEYS */;
/*!40000 ALTER TABLE `blackout_period` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `blackout_profile`
--

DROP TABLE IF EXISTS `blackout_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `blackout_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `entity_name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `blackout_profile`
--

LOCK TABLES `blackout_profile` WRITE;
/*!40000 ALTER TABLE `blackout_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `blackout_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `configuration_file`
--

DROP TABLE IF EXISTS `configuration_file`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `configuration_file` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `configuration_file_type` varchar(255) NOT NULL,
  `contents` longtext NOT NULL,
  `configuration_file_set_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKr0n452el7rk8xu2mdvbri4law` (`configuration_file_set_id`),
  CONSTRAINT `FKr0n452el7rk8xu2mdvbri4law` FOREIGN KEY (`configuration_file_set_id`) REFERENCES `configuration_file_set` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `configuration_file`
--

LOCK TABLES `configuration_file` WRITE;
/*!40000 ALTER TABLE `configuration_file` DISABLE KEYS */;
/*!40000 ALTER TABLE `configuration_file` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `configuration_file_set`
--

DROP TABLE IF EXISTS `configuration_file_set`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `configuration_file_set` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `data_token` varchar(255) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `configuration_file_set`
--

LOCK TABLES `configuration_file_set` WRITE;
/*!40000 ALTER TABLE `configuration_file_set` DISABLE KEYS */;
/*!40000 ALTER TABLE `configuration_file_set` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `connection`
--

DROP TABLE IF EXISTS `connection`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `connection` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `system_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `connection`
--

LOCK TABLES `connection` WRITE;
/*!40000 ALTER TABLE `connection` DISABLE KEYS */;
/*!40000 ALTER TABLE `connection` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `content_screening_profile`
--

DROP TABLE IF EXISTS `content_screening_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `content_screening_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `entity_name` varchar(255) DEFAULT NULL,
  `token_threshold` bigint(20) DEFAULT NULL,
  `spam_word_registry_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKcdkh5u7r9q6ib2ubplrhdj7wk` (`spam_word_registry_id`),
  CONSTRAINT `FKcdkh5u7r9q6ib2ubplrhdj7wk` FOREIGN KEY (`spam_word_registry_id`) REFERENCES `spam_word_registry` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `content_screening_profile`
--

LOCK TABLES `content_screening_profile` WRITE;
/*!40000 ALTER TABLE `content_screening_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `content_screening_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `number_screening_profile`
--

DROP TABLE IF EXISTS `number_screening_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `number_screening_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `entity_name` varchar(255) DEFAULT NULL,
  `checkanumber_systemid` bit(1) NOT NULL,
  `global_submit_barred_flag` bit(1) NOT NULL,
  `turn_onbnumber_postpaid_barring` bit(1) NOT NULL,
  `b_number_black_list_id` bigint(20) DEFAULT NULL,
  `b_number_white_list_id` bigint(20) DEFAULT NULL,
  `vlr_black_list_id` bigint(20) DEFAULT NULL,
  `vlr_white_list_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKb3qlsylw28jx5qfggjuaclfp0` (`b_number_black_list_id`),
  KEY `FKjkr8ik1bwtd0pr7tbstd4n5tb` (`b_number_white_list_id`),
  KEY `FKstp6n5c311tp3j4nrb61n8yui` (`vlr_black_list_id`),
  KEY `FKd5uvc6n70y4jwlcosg139a7va` (`vlr_white_list_id`),
  CONSTRAINT `FKd5uvc6n70y4jwlcosg139a7va` FOREIGN KEY (`vlr_white_list_id`) REFERENCES `prefix_list` (`id`),
  CONSTRAINT `FKb3qlsylw28jx5qfggjuaclfp0` FOREIGN KEY (`b_number_black_list_id`) REFERENCES `prefix_list` (`id`),
  CONSTRAINT `FKjkr8ik1bwtd0pr7tbstd4n5tb` FOREIGN KEY (`b_number_white_list_id`) REFERENCES `prefix_list` (`id`),
  CONSTRAINT `FKstp6n5c311tp3j4nrb61n8yui` FOREIGN KEY (`vlr_black_list_id`) REFERENCES `prefix_list` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `number_screening_profile`
--

LOCK TABLES `number_screening_profile` WRITE;
/*!40000 ALTER TABLE `number_screening_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `number_screening_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `prefix`
--

DROP TABLE IF EXISTS `prefix`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `prefix` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `prefix` varchar(255) DEFAULT NULL,
  `prefix_list_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKf2l1etnoqloea3u7d4hbswyt1` (`prefix_list_id`),
  CONSTRAINT `FKf2l1etnoqloea3u7d4hbswyt1` FOREIGN KEY (`prefix_list_id`) REFERENCES `prefix_list` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `prefix`
--

LOCK TABLES `prefix` WRITE;
/*!40000 ALTER TABLE `prefix` DISABLE KEYS */;
/*!40000 ALTER TABLE `prefix` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `prefix_list`
--

DROP TABLE IF EXISTS `prefix_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `prefix_list` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `prefix_list`
--

LOCK TABLES `prefix_list` WRITE;
/*!40000 ALTER TABLE `prefix_list` DISABLE KEYS */;
/*!40000 ALTER TABLE `prefix_list` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `screening_profile`
--

DROP TABLE IF EXISTS `screening_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `screening_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `enabled` bit(1) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `screening_profile_type` varchar(255) NOT NULL,
  `analysis_profile_id` bigint(20) DEFAULT NULL,
  `analysis_profile_rules_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKii9a83ku7p4342e0mp19vyr04` (`analysis_profile_id`),
  KEY `FKbfbjwf2l99vm2ypde2bp66fub` (`analysis_profile_rules_id`),
  CONSTRAINT `FKbfbjwf2l99vm2ypde2bp66fub` FOREIGN KEY (`analysis_profile_rules_id`) REFERENCES `analysis_profile_rules` (`id`),
  CONSTRAINT `FKii9a83ku7p4342e0mp19vyr04` FOREIGN KEY (`analysis_profile_id`) REFERENCES `analysis_profile` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `screening_profile`
--

LOCK TABLES `screening_profile` WRITE;
/*!40000 ALTER TABLE `screening_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `screening_profile` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `screening_profile_join_prefix_list`
--

DROP TABLE IF EXISTS `screening_profile_join_prefix_list`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `screening_profile_join_prefix_list` (
  `screening_profile_id` bigint(20) NOT NULL,
  `prefix_list_id` bigint(20) NOT NULL,
  PRIMARY KEY (`screening_profile_id`,`prefix_list_id`),
  KEY `FKoow0qq2rouhs2rssjod4eh3at` (`prefix_list_id`),
  CONSTRAINT `FKf2bscfsjju2n3yotog0d067le` FOREIGN KEY (`screening_profile_id`) REFERENCES `screening_profile` (`id`),
  CONSTRAINT `FKoow0qq2rouhs2rssjod4eh3at` FOREIGN KEY (`prefix_list_id`) REFERENCES `prefix_list` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `screening_profile_join_prefix_list`
--

LOCK TABLES `screening_profile_join_prefix_list` WRITE;
/*!40000 ALTER TABLE `screening_profile_join_prefix_list` DISABLE KEYS */;
/*!40000 ALTER TABLE `screening_profile_join_prefix_list` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `spam_word`
--

DROP TABLE IF EXISTS `spam_word`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `spam_word` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  `severity_rating` int(11) DEFAULT NULL,
  `spam_word_registry_id` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `FKhohm9thf7fyqldeweeoumr1kf` (`spam_word_registry_id`),
  CONSTRAINT `FKhohm9thf7fyqldeweeoumr1kf` FOREIGN KEY (`spam_word_registry_id`) REFERENCES `spam_word_registry` (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `spam_word`
--

LOCK TABLES `spam_word` WRITE;
/*!40000 ALTER TABLE `spam_word` DISABLE KEYS */;
/*!40000 ALTER TABLE `spam_word` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `spam_word_registry`
--

DROP TABLE IF EXISTS `spam_word_registry`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `spam_word_registry` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `case_sensitive` bit(1) DEFAULT NULL,
  `count_multiple_occurences` bit(1) DEFAULT NULL,
  `name` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `spam_word_registry`
--

LOCK TABLES `spam_word_registry` WRITE;
/*!40000 ALTER TABLE `spam_word_registry` DISABLE KEYS */;
/*!40000 ALTER TABLE `spam_word_registry` ENABLE KEYS */;
UNLOCK TABLES;

--
-- Table structure for table `throttling_profile`
--

DROP TABLE IF EXISTS `throttling_profile`;
/*!40101 SET @saved_cs_client     = @@character_set_client */;
/*!40101 SET character_set_client = utf8 */;
CREATE TABLE `throttling_profile` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `created_time` datetime DEFAULT NULL,
  `updated_time` datetime DEFAULT NULL,
  `description` varchar(255) DEFAULT NULL,
  `entity_name` varchar(255) DEFAULT NULL,
  `analysis_window_in_seconds` bigint(20) DEFAULT NULL,
  `analysis_window_sms_threshold` bigint(20) DEFAULT NULL,
  `analysis_window_threshold` bigint(20) DEFAULT NULL,
  `number_of_windows_to_analyse` bigint(20) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=8 DEFAULT CHARSET=latin1;
/*!40101 SET character_set_client = @saved_cs_client */;

--
-- Dumping data for table `throttling_profile`
--

LOCK TABLES `throttling_profile` WRITE;
/*!40000 ALTER TABLE `throttling_profile` DISABLE KEYS */;
/*!40000 ALTER TABLE `throttling_profile` ENABLE KEYS */;
UNLOCK TABLES;
/*!40103 SET TIME_ZONE=@OLD_TIME_ZONE */;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;

-- Dump completed on 2017-07-13 16:47:24
