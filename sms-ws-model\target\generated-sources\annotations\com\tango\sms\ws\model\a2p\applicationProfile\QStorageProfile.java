package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QStorageProfile is a Querydsl query type for StorageProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QStorageProfile extends BeanPath<StorageProfile> {

    private static final long serialVersionUID = 1029128004L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QStorageProfile storageProfile = new QStorageProfile("storageProfile");

    public final com.tango.sms.ws.model.common.QResourcePolicy consumerResourcePolicy;

    public final BooleanPath enabled = createBoolean("enabled");

    public final BooleanPath enableMessageReassembly = createBoolean("enableMessageReassembly");

    public final com.tango.sms.ws.model.common.QResourcePolicy storeResourcePolicy;

    public QStorageProfile(String variable) {
        this(StorageProfile.class, forVariable(variable), INITS);
    }

    public QStorageProfile(Path<? extends StorageProfile> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QStorageProfile(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QStorageProfile(PathMetadata metadata, PathInits inits) {
        this(StorageProfile.class, metadata, inits);
    }

    public QStorageProfile(Class<? extends StorageProfile> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.consumerResourcePolicy = inits.isInitialized("consumerResourcePolicy") ? new com.tango.sms.ws.model.common.QResourcePolicy(forProperty("consumerResourcePolicy")) : null;
        this.storeResourcePolicy = inits.isInitialized("storeResourcePolicy") ? new com.tango.sms.ws.model.common.QResourcePolicy(forProperty("storeResourcePolicy")) : null;
    }

}

