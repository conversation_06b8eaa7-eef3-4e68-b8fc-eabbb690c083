package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QBlackoutPeriod is a Querydsl query type for BlackoutPeriod
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QBlackoutPeriod extends EntityPathBase<BlackoutPeriod> {

    private static final long serialVersionUID = -2058359258L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QBlackoutPeriod blackoutPeriod = new QBlackoutPeriod("blackoutPeriod");

    public final com.tango.sms.ws.model.common.QBaseEntity _super = new com.tango.sms.ws.model.common.QBaseEntity(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    public final QBlackoutDuration duration;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final StringPath name = createString("name");

    public QBlackoutPeriod(String variable) {
        this(BlackoutPeriod.class, forVariable(variable), INITS);
    }

    public QBlackoutPeriod(Path<? extends BlackoutPeriod> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QBlackoutPeriod(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QBlackoutPeriod(PathMetadata metadata, PathInits inits) {
        this(BlackoutPeriod.class, metadata, inits);
    }

    public QBlackoutPeriod(Class<? extends BlackoutPeriod> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.duration = inits.isInitialized("duration") ? new QBlackoutDuration(forProperty("duration")) : null;
    }

}

