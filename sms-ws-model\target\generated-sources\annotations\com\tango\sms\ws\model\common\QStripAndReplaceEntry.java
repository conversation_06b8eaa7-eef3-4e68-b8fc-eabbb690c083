package com.tango.sms.ws.model.common;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QStripAndReplaceEntry is a Querydsl query type for StripAndReplaceEntry
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QStripAndReplaceEntry extends EntityPathBase<StripAndReplaceEntry> {

    private static final long serialVersionUID = -1774055600L;

    public static final QStripAndReplaceEntry stripAndReplaceEntry = new QStripAndReplaceEntry("stripAndReplaceEntry");

    public final QBaseEntity _super = new QBaseEntity(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final NumberPath<Integer> maxDigits = createNumber("maxDigits", Integer.class);

    public final NumberPath<Integer> minDigits = createNumber("minDigits", Integer.class);

    public final StringPath replaceNumber = createString("replaceNumber");

    public final EnumPath<com.tango.sms.ws.model.enumeration.NumberPlanIndicator> replaceNumberPlanIndicator = createEnum("replaceNumberPlanIndicator", com.tango.sms.ws.model.enumeration.NumberPlanIndicator.class);

    public final EnumPath<com.tango.sms.ws.model.enumeration.TypeOfNumber> replaceTypeOfNumber = createEnum("replaceTypeOfNumber", com.tango.sms.ws.model.enumeration.TypeOfNumber.class);

    public final StringPath stripNumber = createString("stripNumber");

    public final EnumPath<com.tango.sms.ws.model.enumeration.NumberPlanIndicator> stripNumberPlanIndicator = createEnum("stripNumberPlanIndicator", com.tango.sms.ws.model.enumeration.NumberPlanIndicator.class);

    public final EnumPath<com.tango.sms.ws.model.enumeration.TypeOfNumber> stripTypeOfNumber = createEnum("stripTypeOfNumber", com.tango.sms.ws.model.enumeration.TypeOfNumber.class);

    public QStripAndReplaceEntry(String variable) {
        super(StripAndReplaceEntry.class, forVariable(variable));
    }

    public QStripAndReplaceEntry(Path<? extends StripAndReplaceEntry> path) {
        super(path.getType(), path.getMetadata());
    }

    public QStripAndReplaceEntry(PathMetadata metadata) {
        super(StripAndReplaceEntry.class, metadata);
    }

}

