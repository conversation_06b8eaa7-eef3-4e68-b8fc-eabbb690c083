package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;
import com.querydsl.core.types.dsl.PathInits;


/**
 * QContentScreeningProfile is a Querydsl query type for ContentScreeningProfile
 */
@Generated("com.querydsl.codegen.DefaultEmbeddableSerializer")
public class QContentScreeningProfile extends BeanPath<ContentScreeningProfile> {

    private static final long serialVersionUID = 1615990678L;

    private static final PathInits INITS = PathInits.DIRECT2;

    public static final QContentScreeningProfile contentScreeningProfile = new QContentScreeningProfile("contentScreeningProfile");

    public final BooleanPath enabled = createBoolean("enabled");

    public final com.tango.sms.ws.model.common.QSpamWordRegistry spamWordRegistry;

    public final NumberPath<Long> tokenThreshold = createNumber("tokenThreshold", Long.class);

    public QContentScreeningProfile(String variable) {
        this(ContentScreeningProfile.class, forVariable(variable), INITS);
    }

    public QContentScreeningProfile(Path<? extends ContentScreeningProfile> path) {
        this(path.getType(), path.getMetadata(), PathInits.getFor(path.getMetadata(), INITS));
    }

    public QContentScreeningProfile(PathMetadata metadata) {
        this(metadata, PathInits.getFor(metadata, INITS));
    }

    public QContentScreeningProfile(PathMetadata metadata, PathInits inits) {
        this(ContentScreeningProfile.class, metadata, inits);
    }

    public QContentScreeningProfile(Class<? extends ContentScreeningProfile> type, PathMetadata metadata, PathInits inits) {
        super(type, metadata, inits);
        this.spamWordRegistry = inits.isInitialized("spamWordRegistry") ? new com.tango.sms.ws.model.common.QSpamWordRegistry(forProperty("spamWordRegistry")) : null;
    }

}

