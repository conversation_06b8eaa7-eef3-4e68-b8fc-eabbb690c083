package com.tango.sms.ws.model.common;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QSpamWord is a Querydsl query type for SpamWord
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QSpamWord extends EntityPathBase<SpamWord> {

    private static final long serialVersionUID = 1715449446L;

    public static final QSpamWord spamWord = new QSpamWord("spamWord");

    public final QBaseEntity _super = new QBaseEntity(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final StringPath name = createString("name");

    public final NumberPath<Integer> severityRating = createNumber("severityRating", Integer.class);

    public QSpamWord(String variable) {
        super(SpamWord.class, forVariable(variable));
    }

    public QSpamWord(Path<? extends SpamWord> path) {
        super(path.getType(), path.getMetadata());
    }

    public QSpamWord(PathMetadata metadata) {
        super(SpamWord.class, metadata);
    }

}

