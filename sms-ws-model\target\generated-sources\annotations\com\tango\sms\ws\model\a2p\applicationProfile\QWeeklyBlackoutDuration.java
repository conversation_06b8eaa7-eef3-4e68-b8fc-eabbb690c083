package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QWeeklyBlackoutDuration is a Querydsl query type for WeeklyBlackoutDuration
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QWeeklyBlackoutDuration extends EntityPathBase<WeeklyBlackoutDuration> {

    private static final long serialVersionUID = 1173076698L;

    public static final QWeeklyBlackoutDuration weeklyBlackoutDuration = new QWeeklyBlackoutDuration("weeklyBlackoutDuration");

    public final QBlackoutDuration _super = new QBlackoutDuration(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    public final StringPath daysActive = createString("daysActive");

    //inherited
    public final TimePath<java.sql.Time> endTime = _super.endTime;

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    //inherited
    public final TimePath<java.sql.Time> startTime = _super.startTime;

    public QWeeklyBlackoutDuration(String variable) {
        super(WeeklyBlackoutDuration.class, forVariable(variable));
    }

    public QWeeklyBlackoutDuration(Path<? extends WeeklyBlackoutDuration> path) {
        super(path.getType(), path.getMetadata());
    }

    public QWeeklyBlackoutDuration(PathMetadata metadata) {
        super(WeeklyBlackoutDuration.class, metadata);
    }

}

