package com.tango.sms.ws.model.a2p.applicationProfile;

import static com.querydsl.core.types.PathMetadataFactory.*;

import com.querydsl.core.types.dsl.*;

import com.querydsl.core.types.PathMetadata;
import javax.annotation.processing.Generated;
import com.querydsl.core.types.Path;


/**
 * QBlackoutDuration is a Querydsl query type for BlackoutDuration
 */
@Generated("com.querydsl.codegen.DefaultEntitySerializer")
public class QBlackoutDuration extends EntityPathBase<BlackoutDuration> {

    private static final long serialVersionUID = -529252167L;

    public static final QBlackoutDuration blackoutDuration = new QBlackoutDuration("blackoutDuration");

    public final com.tango.sms.ws.model.common.QBaseEntity _super = new com.tango.sms.ws.model.common.QBaseEntity(this);

    //inherited
    public final DateTimePath<java.util.Date> createdTime = _super.createdTime;

    public final TimePath<java.sql.Time> endTime = createTime("endTime", java.sql.Time.class);

    //inherited
    public final NumberPath<Long> id = _super.id;

    //inherited
    public final DateTimePath<java.util.Date> lastUpdatedTime = _super.lastUpdatedTime;

    public final TimePath<java.sql.Time> startTime = createTime("startTime", java.sql.Time.class);

    public QBlackoutDuration(String variable) {
        super(BlackoutDuration.class, forVariable(variable));
    }

    public QBlackoutDuration(Path<? extends BlackoutDuration> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBlackoutDuration(PathMetadata metadata) {
        super(BlackoutDuration.class, metadata);
    }

}

